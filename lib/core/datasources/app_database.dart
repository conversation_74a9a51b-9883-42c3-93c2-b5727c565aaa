import 'dart:io';
import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;

// Import table definitions
part 'app_database.g.dart';

/// Income table definition
class IncomeTable extends Table {
  IntColumn get id => integer().autoIncrement()();
  DateTimeColumn get date => dateTime()();
  IntColumn get initialMileage => integer()();
  IntColumn get finalMileage => integer()();
  RealColumn get initialGopay => real()();
  RealColumn get initialBca => real()();
  RealColumn get initialCash => real()();
  RealColumn get initialOvo => real()();
  RealColumn get initialBri => real()();
  RealColumn get initialRekpon => real()();
  RealColumn get finalGopay => real()();
  RealColumn get finalBca => real()();
  RealColumn get finalCash => real()();
  RealColumn get finalOvo => real()();
  RealColumn get finalBri => real()();
  RealColumn get finalRekpon => real()();
  TextColumn get uuid => text().nullable()();
  BoolColumn get isDeleted => boolean().withDefault(const Constant(false))();
  BoolColumn get isSynced => boolean().withDefault(const Constant(false))();
  DateTimeColumn get createdAt => dateTime().nullable()();
  DateTimeColumn get updatedAt => dateTime().nullable()();
  DateTimeColumn get deletedAt => dateTime().nullable()();
}

/// Main database class
@DriftDatabase(tables: [IncomeTable])
class AppDatabase extends _$AppDatabase {
  AppDatabase() : super(_openConnection());

  @override
  int get schemaVersion => 1;

  @override
  MigrationStrategy get migration => MigrationStrategy(
    onCreate: (Migrator m) async {
      await m.createAll();
    },
    onUpgrade: (Migrator m, int from, int to) async {
      // Handle database migrations here
    },
  );
}

/// Open database connection
LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, 'drivly.db'));
    return NativeDatabase(file);
  });
}
