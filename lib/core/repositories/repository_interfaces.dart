import 'package:dartz/dartz.dart';
import '../errors/failures.dart';

/// Base repository interface for all entities
abstract class IRepository<T, ID> {
  Future<Either<Failure, List<T>>> getAll();
  Future<Either<Failure, T>> getById(ID id);
  Future<Either<Failure, T>> save(T entity);
  Future<Either<Failure, T>> update(T entity);
  Future<Either<Failure, bool>> delete(ID id);
}

/// Interface for repositories that support date range queries
abstract class IDateRangeRepository<T, ID> extends IRepository<T, ID> {
  Future<Either<Failure, List<T>>> getForDateRange(DateTime start, DateTime end);
  Future<Either<Failure, bool>> checkDateExists(DateTime date, {ID? excludeId});
}

/// Interface for repositories that support synchronization
abstract class ISyncableRepository<T, ID> {
  Future<Either<Failure, List<T>>> getUnsyncedEntities();
  Future<Either<Failure, bool>> markAsSynced(String uuid);
  Future<Either<Failure, bool>> syncEntities();
}

/// Interface for repositories that support soft delete
abstract class ISoftDeleteRepository<T, ID> {
  Future<Either<Failure, bool>> softDelete(ID id);
  Future<Either<Failure, List<T>>> getAllIncludingDeleted();
  Future<Either<Failure, bool>> restore(ID id);
}
