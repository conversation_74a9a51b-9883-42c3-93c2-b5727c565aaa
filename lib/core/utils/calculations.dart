/// Utility class for performing calculations across the application
class Calculations {
  /// Calculate initial capital from all initial balance values
  static double calculateInitialCapital({
    required double initialGopay,
    required double initialBca,
    required double initialCash,
    required double initialOvo,
    required double initialBri,
    required double initialRekpon,
  }) {
    return initialGopay + initialBca + initialCash + initialOvo + initialBri + initialRekpon;
  }

  /// Calculate final result from all final balance values
  static double calculateFinalResult({
    required double finalGopay,
    required double finalBca,
    required double finalCash,
    required double finalOvo,
    required double finalBri,
    required double finalRekpon,
  }) {
    return finalGopay + finalBca + finalCash + finalOvo + finalBri + finalRekpon;
  }

  /// Calculate mileage difference
  static int calculateMileage({
    required int finalMileage,
    required int initialMileage,
  }) {
    return finalMileage - initialMileage;
  }

  /// Calculate net income
  static double calculateNetIncome({
    required double finalResult,
    required double initialCapital,
  }) {
    return finalResult - initialCapital;
  }

  /// Format currency for display
  static String formatCurrency(double amount) {
    if (amount == 0) return '0';
    
    // Handle negative values
    final isNegative = amount < 0;
    final absoluteAmount = amount.abs();
    
    // Format with thousands separator
    final formatted = absoluteAmount.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    );
    
    return isNegative ? '-$formatted' : formatted;
  }

  /// Format mileage for display
  static String formatMileage(int mileage) {
    if (mileage == 0) return '0';
    
    return mileage.toString().replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    );
  }
}
