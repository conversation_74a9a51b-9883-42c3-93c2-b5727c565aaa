import 'package:intl/intl.dart';

/// Utility class for date formatting and manipulation
class DateHelper {
  static final DateFormat _displayFormat = DateFormat('dd MMM yyyy');
  static final DateFormat _shortFormat = DateFormat('dd/MM');
  static final DateFormat _monthYearFormat = DateFormat('MMM yyyy');
  static final DateFormat _dayFormat = DateFormat('dd');
  static final DateFormat _monthFormat = DateFormat('MMM');
  static final DateFormat _yearFormat = DateFormat('yyyy');
  static final DateFormat _isoFormat = DateFormat('yyyy-MM-dd');

  /// Format date for display (e.g., "15 Jan 2024")
  static String formatForDisplay(DateTime date) {
    return _displayFormat.format(date);
  }

  /// Format date in short format (e.g., "15/01")
  static String formatShort(DateTime date) {
    return _shortFormat.format(date);
  }

  /// Format date as month and year (e.g., "Jan 2024")
  static String formatMonthYear(DateTime date) {
    return _monthYearFormat.format(date);
  }

  /// Format date as day only (e.g., "15")
  static String formatDay(DateTime date) {
    return _dayFormat.format(date);
  }

  /// Format date as month only (e.g., "Jan")
  static String formatMonth(DateTime date) {
    return _monthFormat.format(date);
  }

  /// Format date as year only (e.g., "2024")
  static String formatYear(DateTime date) {
    return _yearFormat.format(date);
  }

  /// Format date as ISO string (e.g., "2024-01-15")
  static String formatIso(DateTime date) {
    return _isoFormat.format(date);
  }

  /// Parse ISO date string to DateTime
  static DateTime parseIso(String dateString) {
    return _isoFormat.parse(dateString);
  }

  /// Get start of day
  static DateTime startOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }

  /// Get end of day
  static DateTime endOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day, 23, 59, 59, 999);
  }

  /// Get start of month
  static DateTime startOfMonth(DateTime date) {
    return DateTime(date.year, date.month, 1);
  }

  /// Get end of month
  static DateTime endOfMonth(DateTime date) {
    return DateTime(date.year, date.month + 1, 0, 23, 59, 59, 999);
  }

  /// Check if two dates are the same day
  static bool isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  /// Get days between two dates
  static int daysBetween(DateTime start, DateTime end) {
    return end.difference(start).inDays;
  }

  /// Get default date range (current month)
  static Map<String, DateTime> getDefaultDateRange() {
    final now = DateTime.now();
    return {'start': startOfMonth(now), 'end': endOfMonth(now)};
  }
}
