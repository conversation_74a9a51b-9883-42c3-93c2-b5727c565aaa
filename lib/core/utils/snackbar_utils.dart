import 'package:flutter/material.dart';

/// Utility class for showing snackbars throughout the application
class SnackbarUtils {
  static final GlobalKey<ScaffoldMessengerState> _scaffoldMessengerKey = 
      GlobalKey<ScaffoldMessengerState>();

  /// Get the scaffold messenger key for the app
  static GlobalKey<ScaffoldMessengerState> get scaffoldMessengerKey => 
      _scaffoldMessengerKey;

  /// Show a success snackbar
  static void showSuccess(String message) {
    _showSnackbar(
      message: message,
      backgroundColor: Colors.green,
      icon: Icons.check_circle,
    );
  }

  /// Show an error snackbar
  static void showError(String message) {
    _showSnackbar(
      message: message,
      backgroundColor: Colors.red,
      icon: Icons.error,
    );
  }

  /// Show an info snackbar
  static void showInfo(String message) {
    _showSnackbar(
      message: message,
      backgroundColor: Colors.blue,
      icon: Icons.info,
    );
  }

  /// Show a warning snackbar
  static void showWarning(String message) {
    _showSnackbar(
      message: message,
      backgroundColor: Colors.orange,
      icon: Icons.warning,
    );
  }

  /// Show a loading snackbar
  static void showLoading({String message = 'Loading...'}) {
    _scaffoldMessengerKey.currentState?.showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            const SizedBox(width: 16),
            Text(message),
          ],
        ),
        backgroundColor: Colors.grey[700],
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 30), // Long duration for loading
      ),
    );
  }

  /// Hide current snackbar
  static void hide() {
    _scaffoldMessengerKey.currentState?.hideCurrentSnackBar();
  }

  /// Show a generic snackbar
  static void _showSnackbar({
    required String message,
    required Color backgroundColor,
    required IconData icon,
  }) {
    _scaffoldMessengerKey.currentState?.showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(icon, color: Colors.white, size: 20),
            const SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: backgroundColor,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 4),
      ),
    );
  }
}
