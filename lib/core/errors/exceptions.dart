/// Base exception class for the application
abstract class AppException implements Exception {
  final String message;
  const AppException(this.message);

  @override
  String toString() => message;
}

/// Database related exceptions
class DatabaseException extends AppException {
  const DatabaseException(super.message);
}

/// Network related exceptions
class NetworkException extends AppException {
  const NetworkException(super.message);
}

/// Sync related exceptions
class SyncException extends AppException {
  const SyncException(super.message);
}

/// Validation related exceptions
class ValidationException extends AppException {
  const ValidationException(super.message);
}

/// Business logic related exceptions
class BusinessLogicException extends AppException {
  const BusinessLogicException(super.message);
}
