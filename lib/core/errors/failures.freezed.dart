// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'failures.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

/// @nodoc
mixin _$Failure {
  String get message => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message) database,
    required TResult Function(String message) network,
    required TResult Function(String message) notFound,
    required TResult Function(String message) invalidInput,
    required TResult Function(String message) businessLogic,
    required TResult Function(String message) unexpected,
    required TResult Function(String message) sync,
    required TResult Function(String message) permission,
    required TResult Function(String message) validation,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message)? database,
    TResult? Function(String message)? network,
    TResult? Function(String message)? notFound,
    TResult? Function(String message)? invalidInput,
    TResult? Function(String message)? businessLogic,
    TResult? Function(String message)? unexpected,
    TResult? Function(String message)? sync,
    TResult? Function(String message)? permission,
    TResult? Function(String message)? validation,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message)? database,
    TResult Function(String message)? network,
    TResult Function(String message)? notFound,
    TResult Function(String message)? invalidInput,
    TResult Function(String message)? businessLogic,
    TResult Function(String message)? unexpected,
    TResult Function(String message)? sync,
    TResult Function(String message)? permission,
    TResult Function(String message)? validation,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DatabaseFailure value) database,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(NotFoundFailure value) notFound,
    required TResult Function(InvalidInputFailure value) invalidInput,
    required TResult Function(BusinessLogicFailure value) businessLogic,
    required TResult Function(UnexpectedFailure value) unexpected,
    required TResult Function(SyncFailure value) sync,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(ValidationFailure value) validation,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DatabaseFailure value)? database,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(NotFoundFailure value)? notFound,
    TResult? Function(InvalidInputFailure value)? invalidInput,
    TResult? Function(BusinessLogicFailure value)? businessLogic,
    TResult? Function(UnexpectedFailure value)? unexpected,
    TResult? Function(SyncFailure value)? sync,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(ValidationFailure value)? validation,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DatabaseFailure value)? database,
    TResult Function(NetworkFailure value)? network,
    TResult Function(NotFoundFailure value)? notFound,
    TResult Function(InvalidInputFailure value)? invalidInput,
    TResult Function(BusinessLogicFailure value)? businessLogic,
    TResult Function(UnexpectedFailure value)? unexpected,
    TResult Function(SyncFailure value)? sync,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(ValidationFailure value)? validation,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FailureCopyWith<Failure> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FailureCopyWith<$Res> {
  factory $FailureCopyWith(Failure value, $Res Function(Failure) then) =
      _$FailureCopyWithImpl<$Res, Failure>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class _$FailureCopyWithImpl<$Res, $Val extends Failure>
    implements $FailureCopyWith<$Res> {
  _$FailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null}) {
    return _then(
      _value.copyWith(
            message: null == message
                ? _value.message
                : message // ignore: cast_nullable_to_non_nullable
                      as String,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$DatabaseFailureImplCopyWith<$Res>
    implements $FailureCopyWith<$Res> {
  factory _$$DatabaseFailureImplCopyWith(
    _$DatabaseFailureImpl value,
    $Res Function(_$DatabaseFailureImpl) then,
  ) = __$$DatabaseFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$DatabaseFailureImplCopyWithImpl<$Res>
    extends _$FailureCopyWithImpl<$Res, _$DatabaseFailureImpl>
    implements _$$DatabaseFailureImplCopyWith<$Res> {
  __$$DatabaseFailureImplCopyWithImpl(
    _$DatabaseFailureImpl _value,
    $Res Function(_$DatabaseFailureImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null}) {
    return _then(
      _$DatabaseFailureImpl(
        null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc

class _$DatabaseFailureImpl implements DatabaseFailure {
  const _$DatabaseFailureImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'Failure.database(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DatabaseFailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DatabaseFailureImplCopyWith<_$DatabaseFailureImpl> get copyWith =>
      __$$DatabaseFailureImplCopyWithImpl<_$DatabaseFailureImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message) database,
    required TResult Function(String message) network,
    required TResult Function(String message) notFound,
    required TResult Function(String message) invalidInput,
    required TResult Function(String message) businessLogic,
    required TResult Function(String message) unexpected,
    required TResult Function(String message) sync,
    required TResult Function(String message) permission,
    required TResult Function(String message) validation,
  }) {
    return database(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message)? database,
    TResult? Function(String message)? network,
    TResult? Function(String message)? notFound,
    TResult? Function(String message)? invalidInput,
    TResult? Function(String message)? businessLogic,
    TResult? Function(String message)? unexpected,
    TResult? Function(String message)? sync,
    TResult? Function(String message)? permission,
    TResult? Function(String message)? validation,
  }) {
    return database?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message)? database,
    TResult Function(String message)? network,
    TResult Function(String message)? notFound,
    TResult Function(String message)? invalidInput,
    TResult Function(String message)? businessLogic,
    TResult Function(String message)? unexpected,
    TResult Function(String message)? sync,
    TResult Function(String message)? permission,
    TResult Function(String message)? validation,
    required TResult orElse(),
  }) {
    if (database != null) {
      return database(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DatabaseFailure value) database,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(NotFoundFailure value) notFound,
    required TResult Function(InvalidInputFailure value) invalidInput,
    required TResult Function(BusinessLogicFailure value) businessLogic,
    required TResult Function(UnexpectedFailure value) unexpected,
    required TResult Function(SyncFailure value) sync,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(ValidationFailure value) validation,
  }) {
    return database(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DatabaseFailure value)? database,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(NotFoundFailure value)? notFound,
    TResult? Function(InvalidInputFailure value)? invalidInput,
    TResult? Function(BusinessLogicFailure value)? businessLogic,
    TResult? Function(UnexpectedFailure value)? unexpected,
    TResult? Function(SyncFailure value)? sync,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(ValidationFailure value)? validation,
  }) {
    return database?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DatabaseFailure value)? database,
    TResult Function(NetworkFailure value)? network,
    TResult Function(NotFoundFailure value)? notFound,
    TResult Function(InvalidInputFailure value)? invalidInput,
    TResult Function(BusinessLogicFailure value)? businessLogic,
    TResult Function(UnexpectedFailure value)? unexpected,
    TResult Function(SyncFailure value)? sync,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(ValidationFailure value)? validation,
    required TResult orElse(),
  }) {
    if (database != null) {
      return database(this);
    }
    return orElse();
  }
}

abstract class DatabaseFailure implements Failure {
  const factory DatabaseFailure(final String message) = _$DatabaseFailureImpl;

  @override
  String get message;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DatabaseFailureImplCopyWith<_$DatabaseFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$NetworkFailureImplCopyWith<$Res>
    implements $FailureCopyWith<$Res> {
  factory _$$NetworkFailureImplCopyWith(
    _$NetworkFailureImpl value,
    $Res Function(_$NetworkFailureImpl) then,
  ) = __$$NetworkFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$NetworkFailureImplCopyWithImpl<$Res>
    extends _$FailureCopyWithImpl<$Res, _$NetworkFailureImpl>
    implements _$$NetworkFailureImplCopyWith<$Res> {
  __$$NetworkFailureImplCopyWithImpl(
    _$NetworkFailureImpl _value,
    $Res Function(_$NetworkFailureImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null}) {
    return _then(
      _$NetworkFailureImpl(
        null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc

class _$NetworkFailureImpl implements NetworkFailure {
  const _$NetworkFailureImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'Failure.network(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NetworkFailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NetworkFailureImplCopyWith<_$NetworkFailureImpl> get copyWith =>
      __$$NetworkFailureImplCopyWithImpl<_$NetworkFailureImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message) database,
    required TResult Function(String message) network,
    required TResult Function(String message) notFound,
    required TResult Function(String message) invalidInput,
    required TResult Function(String message) businessLogic,
    required TResult Function(String message) unexpected,
    required TResult Function(String message) sync,
    required TResult Function(String message) permission,
    required TResult Function(String message) validation,
  }) {
    return network(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message)? database,
    TResult? Function(String message)? network,
    TResult? Function(String message)? notFound,
    TResult? Function(String message)? invalidInput,
    TResult? Function(String message)? businessLogic,
    TResult? Function(String message)? unexpected,
    TResult? Function(String message)? sync,
    TResult? Function(String message)? permission,
    TResult? Function(String message)? validation,
  }) {
    return network?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message)? database,
    TResult Function(String message)? network,
    TResult Function(String message)? notFound,
    TResult Function(String message)? invalidInput,
    TResult Function(String message)? businessLogic,
    TResult Function(String message)? unexpected,
    TResult Function(String message)? sync,
    TResult Function(String message)? permission,
    TResult Function(String message)? validation,
    required TResult orElse(),
  }) {
    if (network != null) {
      return network(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DatabaseFailure value) database,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(NotFoundFailure value) notFound,
    required TResult Function(InvalidInputFailure value) invalidInput,
    required TResult Function(BusinessLogicFailure value) businessLogic,
    required TResult Function(UnexpectedFailure value) unexpected,
    required TResult Function(SyncFailure value) sync,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(ValidationFailure value) validation,
  }) {
    return network(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DatabaseFailure value)? database,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(NotFoundFailure value)? notFound,
    TResult? Function(InvalidInputFailure value)? invalidInput,
    TResult? Function(BusinessLogicFailure value)? businessLogic,
    TResult? Function(UnexpectedFailure value)? unexpected,
    TResult? Function(SyncFailure value)? sync,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(ValidationFailure value)? validation,
  }) {
    return network?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DatabaseFailure value)? database,
    TResult Function(NetworkFailure value)? network,
    TResult Function(NotFoundFailure value)? notFound,
    TResult Function(InvalidInputFailure value)? invalidInput,
    TResult Function(BusinessLogicFailure value)? businessLogic,
    TResult Function(UnexpectedFailure value)? unexpected,
    TResult Function(SyncFailure value)? sync,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(ValidationFailure value)? validation,
    required TResult orElse(),
  }) {
    if (network != null) {
      return network(this);
    }
    return orElse();
  }
}

abstract class NetworkFailure implements Failure {
  const factory NetworkFailure(final String message) = _$NetworkFailureImpl;

  @override
  String get message;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NetworkFailureImplCopyWith<_$NetworkFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$NotFoundFailureImplCopyWith<$Res>
    implements $FailureCopyWith<$Res> {
  factory _$$NotFoundFailureImplCopyWith(
    _$NotFoundFailureImpl value,
    $Res Function(_$NotFoundFailureImpl) then,
  ) = __$$NotFoundFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$NotFoundFailureImplCopyWithImpl<$Res>
    extends _$FailureCopyWithImpl<$Res, _$NotFoundFailureImpl>
    implements _$$NotFoundFailureImplCopyWith<$Res> {
  __$$NotFoundFailureImplCopyWithImpl(
    _$NotFoundFailureImpl _value,
    $Res Function(_$NotFoundFailureImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null}) {
    return _then(
      _$NotFoundFailureImpl(
        null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc

class _$NotFoundFailureImpl implements NotFoundFailure {
  const _$NotFoundFailureImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'Failure.notFound(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NotFoundFailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NotFoundFailureImplCopyWith<_$NotFoundFailureImpl> get copyWith =>
      __$$NotFoundFailureImplCopyWithImpl<_$NotFoundFailureImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message) database,
    required TResult Function(String message) network,
    required TResult Function(String message) notFound,
    required TResult Function(String message) invalidInput,
    required TResult Function(String message) businessLogic,
    required TResult Function(String message) unexpected,
    required TResult Function(String message) sync,
    required TResult Function(String message) permission,
    required TResult Function(String message) validation,
  }) {
    return notFound(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message)? database,
    TResult? Function(String message)? network,
    TResult? Function(String message)? notFound,
    TResult? Function(String message)? invalidInput,
    TResult? Function(String message)? businessLogic,
    TResult? Function(String message)? unexpected,
    TResult? Function(String message)? sync,
    TResult? Function(String message)? permission,
    TResult? Function(String message)? validation,
  }) {
    return notFound?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message)? database,
    TResult Function(String message)? network,
    TResult Function(String message)? notFound,
    TResult Function(String message)? invalidInput,
    TResult Function(String message)? businessLogic,
    TResult Function(String message)? unexpected,
    TResult Function(String message)? sync,
    TResult Function(String message)? permission,
    TResult Function(String message)? validation,
    required TResult orElse(),
  }) {
    if (notFound != null) {
      return notFound(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DatabaseFailure value) database,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(NotFoundFailure value) notFound,
    required TResult Function(InvalidInputFailure value) invalidInput,
    required TResult Function(BusinessLogicFailure value) businessLogic,
    required TResult Function(UnexpectedFailure value) unexpected,
    required TResult Function(SyncFailure value) sync,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(ValidationFailure value) validation,
  }) {
    return notFound(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DatabaseFailure value)? database,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(NotFoundFailure value)? notFound,
    TResult? Function(InvalidInputFailure value)? invalidInput,
    TResult? Function(BusinessLogicFailure value)? businessLogic,
    TResult? Function(UnexpectedFailure value)? unexpected,
    TResult? Function(SyncFailure value)? sync,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(ValidationFailure value)? validation,
  }) {
    return notFound?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DatabaseFailure value)? database,
    TResult Function(NetworkFailure value)? network,
    TResult Function(NotFoundFailure value)? notFound,
    TResult Function(InvalidInputFailure value)? invalidInput,
    TResult Function(BusinessLogicFailure value)? businessLogic,
    TResult Function(UnexpectedFailure value)? unexpected,
    TResult Function(SyncFailure value)? sync,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(ValidationFailure value)? validation,
    required TResult orElse(),
  }) {
    if (notFound != null) {
      return notFound(this);
    }
    return orElse();
  }
}

abstract class NotFoundFailure implements Failure {
  const factory NotFoundFailure(final String message) = _$NotFoundFailureImpl;

  @override
  String get message;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NotFoundFailureImplCopyWith<_$NotFoundFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$InvalidInputFailureImplCopyWith<$Res>
    implements $FailureCopyWith<$Res> {
  factory _$$InvalidInputFailureImplCopyWith(
    _$InvalidInputFailureImpl value,
    $Res Function(_$InvalidInputFailureImpl) then,
  ) = __$$InvalidInputFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$InvalidInputFailureImplCopyWithImpl<$Res>
    extends _$FailureCopyWithImpl<$Res, _$InvalidInputFailureImpl>
    implements _$$InvalidInputFailureImplCopyWith<$Res> {
  __$$InvalidInputFailureImplCopyWithImpl(
    _$InvalidInputFailureImpl _value,
    $Res Function(_$InvalidInputFailureImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null}) {
    return _then(
      _$InvalidInputFailureImpl(
        null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc

class _$InvalidInputFailureImpl implements InvalidInputFailure {
  const _$InvalidInputFailureImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'Failure.invalidInput(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InvalidInputFailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$InvalidInputFailureImplCopyWith<_$InvalidInputFailureImpl> get copyWith =>
      __$$InvalidInputFailureImplCopyWithImpl<_$InvalidInputFailureImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message) database,
    required TResult Function(String message) network,
    required TResult Function(String message) notFound,
    required TResult Function(String message) invalidInput,
    required TResult Function(String message) businessLogic,
    required TResult Function(String message) unexpected,
    required TResult Function(String message) sync,
    required TResult Function(String message) permission,
    required TResult Function(String message) validation,
  }) {
    return invalidInput(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message)? database,
    TResult? Function(String message)? network,
    TResult? Function(String message)? notFound,
    TResult? Function(String message)? invalidInput,
    TResult? Function(String message)? businessLogic,
    TResult? Function(String message)? unexpected,
    TResult? Function(String message)? sync,
    TResult? Function(String message)? permission,
    TResult? Function(String message)? validation,
  }) {
    return invalidInput?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message)? database,
    TResult Function(String message)? network,
    TResult Function(String message)? notFound,
    TResult Function(String message)? invalidInput,
    TResult Function(String message)? businessLogic,
    TResult Function(String message)? unexpected,
    TResult Function(String message)? sync,
    TResult Function(String message)? permission,
    TResult Function(String message)? validation,
    required TResult orElse(),
  }) {
    if (invalidInput != null) {
      return invalidInput(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DatabaseFailure value) database,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(NotFoundFailure value) notFound,
    required TResult Function(InvalidInputFailure value) invalidInput,
    required TResult Function(BusinessLogicFailure value) businessLogic,
    required TResult Function(UnexpectedFailure value) unexpected,
    required TResult Function(SyncFailure value) sync,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(ValidationFailure value) validation,
  }) {
    return invalidInput(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DatabaseFailure value)? database,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(NotFoundFailure value)? notFound,
    TResult? Function(InvalidInputFailure value)? invalidInput,
    TResult? Function(BusinessLogicFailure value)? businessLogic,
    TResult? Function(UnexpectedFailure value)? unexpected,
    TResult? Function(SyncFailure value)? sync,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(ValidationFailure value)? validation,
  }) {
    return invalidInput?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DatabaseFailure value)? database,
    TResult Function(NetworkFailure value)? network,
    TResult Function(NotFoundFailure value)? notFound,
    TResult Function(InvalidInputFailure value)? invalidInput,
    TResult Function(BusinessLogicFailure value)? businessLogic,
    TResult Function(UnexpectedFailure value)? unexpected,
    TResult Function(SyncFailure value)? sync,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(ValidationFailure value)? validation,
    required TResult orElse(),
  }) {
    if (invalidInput != null) {
      return invalidInput(this);
    }
    return orElse();
  }
}

abstract class InvalidInputFailure implements Failure {
  const factory InvalidInputFailure(final String message) =
      _$InvalidInputFailureImpl;

  @override
  String get message;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$InvalidInputFailureImplCopyWith<_$InvalidInputFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$BusinessLogicFailureImplCopyWith<$Res>
    implements $FailureCopyWith<$Res> {
  factory _$$BusinessLogicFailureImplCopyWith(
    _$BusinessLogicFailureImpl value,
    $Res Function(_$BusinessLogicFailureImpl) then,
  ) = __$$BusinessLogicFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$BusinessLogicFailureImplCopyWithImpl<$Res>
    extends _$FailureCopyWithImpl<$Res, _$BusinessLogicFailureImpl>
    implements _$$BusinessLogicFailureImplCopyWith<$Res> {
  __$$BusinessLogicFailureImplCopyWithImpl(
    _$BusinessLogicFailureImpl _value,
    $Res Function(_$BusinessLogicFailureImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null}) {
    return _then(
      _$BusinessLogicFailureImpl(
        null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc

class _$BusinessLogicFailureImpl implements BusinessLogicFailure {
  const _$BusinessLogicFailureImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'Failure.businessLogic(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BusinessLogicFailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BusinessLogicFailureImplCopyWith<_$BusinessLogicFailureImpl>
  get copyWith =>
      __$$BusinessLogicFailureImplCopyWithImpl<_$BusinessLogicFailureImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message) database,
    required TResult Function(String message) network,
    required TResult Function(String message) notFound,
    required TResult Function(String message) invalidInput,
    required TResult Function(String message) businessLogic,
    required TResult Function(String message) unexpected,
    required TResult Function(String message) sync,
    required TResult Function(String message) permission,
    required TResult Function(String message) validation,
  }) {
    return businessLogic(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message)? database,
    TResult? Function(String message)? network,
    TResult? Function(String message)? notFound,
    TResult? Function(String message)? invalidInput,
    TResult? Function(String message)? businessLogic,
    TResult? Function(String message)? unexpected,
    TResult? Function(String message)? sync,
    TResult? Function(String message)? permission,
    TResult? Function(String message)? validation,
  }) {
    return businessLogic?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message)? database,
    TResult Function(String message)? network,
    TResult Function(String message)? notFound,
    TResult Function(String message)? invalidInput,
    TResult Function(String message)? businessLogic,
    TResult Function(String message)? unexpected,
    TResult Function(String message)? sync,
    TResult Function(String message)? permission,
    TResult Function(String message)? validation,
    required TResult orElse(),
  }) {
    if (businessLogic != null) {
      return businessLogic(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DatabaseFailure value) database,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(NotFoundFailure value) notFound,
    required TResult Function(InvalidInputFailure value) invalidInput,
    required TResult Function(BusinessLogicFailure value) businessLogic,
    required TResult Function(UnexpectedFailure value) unexpected,
    required TResult Function(SyncFailure value) sync,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(ValidationFailure value) validation,
  }) {
    return businessLogic(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DatabaseFailure value)? database,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(NotFoundFailure value)? notFound,
    TResult? Function(InvalidInputFailure value)? invalidInput,
    TResult? Function(BusinessLogicFailure value)? businessLogic,
    TResult? Function(UnexpectedFailure value)? unexpected,
    TResult? Function(SyncFailure value)? sync,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(ValidationFailure value)? validation,
  }) {
    return businessLogic?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DatabaseFailure value)? database,
    TResult Function(NetworkFailure value)? network,
    TResult Function(NotFoundFailure value)? notFound,
    TResult Function(InvalidInputFailure value)? invalidInput,
    TResult Function(BusinessLogicFailure value)? businessLogic,
    TResult Function(UnexpectedFailure value)? unexpected,
    TResult Function(SyncFailure value)? sync,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(ValidationFailure value)? validation,
    required TResult orElse(),
  }) {
    if (businessLogic != null) {
      return businessLogic(this);
    }
    return orElse();
  }
}

abstract class BusinessLogicFailure implements Failure {
  const factory BusinessLogicFailure(final String message) =
      _$BusinessLogicFailureImpl;

  @override
  String get message;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BusinessLogicFailureImplCopyWith<_$BusinessLogicFailureImpl>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UnexpectedFailureImplCopyWith<$Res>
    implements $FailureCopyWith<$Res> {
  factory _$$UnexpectedFailureImplCopyWith(
    _$UnexpectedFailureImpl value,
    $Res Function(_$UnexpectedFailureImpl) then,
  ) = __$$UnexpectedFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$UnexpectedFailureImplCopyWithImpl<$Res>
    extends _$FailureCopyWithImpl<$Res, _$UnexpectedFailureImpl>
    implements _$$UnexpectedFailureImplCopyWith<$Res> {
  __$$UnexpectedFailureImplCopyWithImpl(
    _$UnexpectedFailureImpl _value,
    $Res Function(_$UnexpectedFailureImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null}) {
    return _then(
      _$UnexpectedFailureImpl(
        null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc

class _$UnexpectedFailureImpl implements UnexpectedFailure {
  const _$UnexpectedFailureImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'Failure.unexpected(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UnexpectedFailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UnexpectedFailureImplCopyWith<_$UnexpectedFailureImpl> get copyWith =>
      __$$UnexpectedFailureImplCopyWithImpl<_$UnexpectedFailureImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message) database,
    required TResult Function(String message) network,
    required TResult Function(String message) notFound,
    required TResult Function(String message) invalidInput,
    required TResult Function(String message) businessLogic,
    required TResult Function(String message) unexpected,
    required TResult Function(String message) sync,
    required TResult Function(String message) permission,
    required TResult Function(String message) validation,
  }) {
    return unexpected(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message)? database,
    TResult? Function(String message)? network,
    TResult? Function(String message)? notFound,
    TResult? Function(String message)? invalidInput,
    TResult? Function(String message)? businessLogic,
    TResult? Function(String message)? unexpected,
    TResult? Function(String message)? sync,
    TResult? Function(String message)? permission,
    TResult? Function(String message)? validation,
  }) {
    return unexpected?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message)? database,
    TResult Function(String message)? network,
    TResult Function(String message)? notFound,
    TResult Function(String message)? invalidInput,
    TResult Function(String message)? businessLogic,
    TResult Function(String message)? unexpected,
    TResult Function(String message)? sync,
    TResult Function(String message)? permission,
    TResult Function(String message)? validation,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DatabaseFailure value) database,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(NotFoundFailure value) notFound,
    required TResult Function(InvalidInputFailure value) invalidInput,
    required TResult Function(BusinessLogicFailure value) businessLogic,
    required TResult Function(UnexpectedFailure value) unexpected,
    required TResult Function(SyncFailure value) sync,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(ValidationFailure value) validation,
  }) {
    return unexpected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DatabaseFailure value)? database,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(NotFoundFailure value)? notFound,
    TResult? Function(InvalidInputFailure value)? invalidInput,
    TResult? Function(BusinessLogicFailure value)? businessLogic,
    TResult? Function(UnexpectedFailure value)? unexpected,
    TResult? Function(SyncFailure value)? sync,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(ValidationFailure value)? validation,
  }) {
    return unexpected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DatabaseFailure value)? database,
    TResult Function(NetworkFailure value)? network,
    TResult Function(NotFoundFailure value)? notFound,
    TResult Function(InvalidInputFailure value)? invalidInput,
    TResult Function(BusinessLogicFailure value)? businessLogic,
    TResult Function(UnexpectedFailure value)? unexpected,
    TResult Function(SyncFailure value)? sync,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(ValidationFailure value)? validation,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(this);
    }
    return orElse();
  }
}

abstract class UnexpectedFailure implements Failure {
  const factory UnexpectedFailure(final String message) =
      _$UnexpectedFailureImpl;

  @override
  String get message;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UnexpectedFailureImplCopyWith<_$UnexpectedFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SyncFailureImplCopyWith<$Res>
    implements $FailureCopyWith<$Res> {
  factory _$$SyncFailureImplCopyWith(
    _$SyncFailureImpl value,
    $Res Function(_$SyncFailureImpl) then,
  ) = __$$SyncFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$SyncFailureImplCopyWithImpl<$Res>
    extends _$FailureCopyWithImpl<$Res, _$SyncFailureImpl>
    implements _$$SyncFailureImplCopyWith<$Res> {
  __$$SyncFailureImplCopyWithImpl(
    _$SyncFailureImpl _value,
    $Res Function(_$SyncFailureImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null}) {
    return _then(
      _$SyncFailureImpl(
        null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc

class _$SyncFailureImpl implements SyncFailure {
  const _$SyncFailureImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'Failure.sync(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SyncFailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SyncFailureImplCopyWith<_$SyncFailureImpl> get copyWith =>
      __$$SyncFailureImplCopyWithImpl<_$SyncFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message) database,
    required TResult Function(String message) network,
    required TResult Function(String message) notFound,
    required TResult Function(String message) invalidInput,
    required TResult Function(String message) businessLogic,
    required TResult Function(String message) unexpected,
    required TResult Function(String message) sync,
    required TResult Function(String message) permission,
    required TResult Function(String message) validation,
  }) {
    return sync(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message)? database,
    TResult? Function(String message)? network,
    TResult? Function(String message)? notFound,
    TResult? Function(String message)? invalidInput,
    TResult? Function(String message)? businessLogic,
    TResult? Function(String message)? unexpected,
    TResult? Function(String message)? sync,
    TResult? Function(String message)? permission,
    TResult? Function(String message)? validation,
  }) {
    return sync?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message)? database,
    TResult Function(String message)? network,
    TResult Function(String message)? notFound,
    TResult Function(String message)? invalidInput,
    TResult Function(String message)? businessLogic,
    TResult Function(String message)? unexpected,
    TResult Function(String message)? sync,
    TResult Function(String message)? permission,
    TResult Function(String message)? validation,
    required TResult orElse(),
  }) {
    if (sync != null) {
      return sync(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DatabaseFailure value) database,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(NotFoundFailure value) notFound,
    required TResult Function(InvalidInputFailure value) invalidInput,
    required TResult Function(BusinessLogicFailure value) businessLogic,
    required TResult Function(UnexpectedFailure value) unexpected,
    required TResult Function(SyncFailure value) sync,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(ValidationFailure value) validation,
  }) {
    return sync(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DatabaseFailure value)? database,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(NotFoundFailure value)? notFound,
    TResult? Function(InvalidInputFailure value)? invalidInput,
    TResult? Function(BusinessLogicFailure value)? businessLogic,
    TResult? Function(UnexpectedFailure value)? unexpected,
    TResult? Function(SyncFailure value)? sync,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(ValidationFailure value)? validation,
  }) {
    return sync?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DatabaseFailure value)? database,
    TResult Function(NetworkFailure value)? network,
    TResult Function(NotFoundFailure value)? notFound,
    TResult Function(InvalidInputFailure value)? invalidInput,
    TResult Function(BusinessLogicFailure value)? businessLogic,
    TResult Function(UnexpectedFailure value)? unexpected,
    TResult Function(SyncFailure value)? sync,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(ValidationFailure value)? validation,
    required TResult orElse(),
  }) {
    if (sync != null) {
      return sync(this);
    }
    return orElse();
  }
}

abstract class SyncFailure implements Failure {
  const factory SyncFailure(final String message) = _$SyncFailureImpl;

  @override
  String get message;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SyncFailureImplCopyWith<_$SyncFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PermissionFailureImplCopyWith<$Res>
    implements $FailureCopyWith<$Res> {
  factory _$$PermissionFailureImplCopyWith(
    _$PermissionFailureImpl value,
    $Res Function(_$PermissionFailureImpl) then,
  ) = __$$PermissionFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$PermissionFailureImplCopyWithImpl<$Res>
    extends _$FailureCopyWithImpl<$Res, _$PermissionFailureImpl>
    implements _$$PermissionFailureImplCopyWith<$Res> {
  __$$PermissionFailureImplCopyWithImpl(
    _$PermissionFailureImpl _value,
    $Res Function(_$PermissionFailureImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null}) {
    return _then(
      _$PermissionFailureImpl(
        null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc

class _$PermissionFailureImpl implements PermissionFailure {
  const _$PermissionFailureImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'Failure.permission(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PermissionFailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PermissionFailureImplCopyWith<_$PermissionFailureImpl> get copyWith =>
      __$$PermissionFailureImplCopyWithImpl<_$PermissionFailureImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message) database,
    required TResult Function(String message) network,
    required TResult Function(String message) notFound,
    required TResult Function(String message) invalidInput,
    required TResult Function(String message) businessLogic,
    required TResult Function(String message) unexpected,
    required TResult Function(String message) sync,
    required TResult Function(String message) permission,
    required TResult Function(String message) validation,
  }) {
    return permission(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message)? database,
    TResult? Function(String message)? network,
    TResult? Function(String message)? notFound,
    TResult? Function(String message)? invalidInput,
    TResult? Function(String message)? businessLogic,
    TResult? Function(String message)? unexpected,
    TResult? Function(String message)? sync,
    TResult? Function(String message)? permission,
    TResult? Function(String message)? validation,
  }) {
    return permission?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message)? database,
    TResult Function(String message)? network,
    TResult Function(String message)? notFound,
    TResult Function(String message)? invalidInput,
    TResult Function(String message)? businessLogic,
    TResult Function(String message)? unexpected,
    TResult Function(String message)? sync,
    TResult Function(String message)? permission,
    TResult Function(String message)? validation,
    required TResult orElse(),
  }) {
    if (permission != null) {
      return permission(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DatabaseFailure value) database,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(NotFoundFailure value) notFound,
    required TResult Function(InvalidInputFailure value) invalidInput,
    required TResult Function(BusinessLogicFailure value) businessLogic,
    required TResult Function(UnexpectedFailure value) unexpected,
    required TResult Function(SyncFailure value) sync,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(ValidationFailure value) validation,
  }) {
    return permission(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DatabaseFailure value)? database,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(NotFoundFailure value)? notFound,
    TResult? Function(InvalidInputFailure value)? invalidInput,
    TResult? Function(BusinessLogicFailure value)? businessLogic,
    TResult? Function(UnexpectedFailure value)? unexpected,
    TResult? Function(SyncFailure value)? sync,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(ValidationFailure value)? validation,
  }) {
    return permission?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DatabaseFailure value)? database,
    TResult Function(NetworkFailure value)? network,
    TResult Function(NotFoundFailure value)? notFound,
    TResult Function(InvalidInputFailure value)? invalidInput,
    TResult Function(BusinessLogicFailure value)? businessLogic,
    TResult Function(UnexpectedFailure value)? unexpected,
    TResult Function(SyncFailure value)? sync,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(ValidationFailure value)? validation,
    required TResult orElse(),
  }) {
    if (permission != null) {
      return permission(this);
    }
    return orElse();
  }
}

abstract class PermissionFailure implements Failure {
  const factory PermissionFailure(final String message) =
      _$PermissionFailureImpl;

  @override
  String get message;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PermissionFailureImplCopyWith<_$PermissionFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ValidationFailureImplCopyWith<$Res>
    implements $FailureCopyWith<$Res> {
  factory _$$ValidationFailureImplCopyWith(
    _$ValidationFailureImpl value,
    $Res Function(_$ValidationFailureImpl) then,
  ) = __$$ValidationFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$ValidationFailureImplCopyWithImpl<$Res>
    extends _$FailureCopyWithImpl<$Res, _$ValidationFailureImpl>
    implements _$$ValidationFailureImplCopyWith<$Res> {
  __$$ValidationFailureImplCopyWithImpl(
    _$ValidationFailureImpl _value,
    $Res Function(_$ValidationFailureImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null}) {
    return _then(
      _$ValidationFailureImpl(
        null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc

class _$ValidationFailureImpl implements ValidationFailure {
  const _$ValidationFailureImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'Failure.validation(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ValidationFailureImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ValidationFailureImplCopyWith<_$ValidationFailureImpl> get copyWith =>
      __$$ValidationFailureImplCopyWithImpl<_$ValidationFailureImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message) database,
    required TResult Function(String message) network,
    required TResult Function(String message) notFound,
    required TResult Function(String message) invalidInput,
    required TResult Function(String message) businessLogic,
    required TResult Function(String message) unexpected,
    required TResult Function(String message) sync,
    required TResult Function(String message) permission,
    required TResult Function(String message) validation,
  }) {
    return validation(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message)? database,
    TResult? Function(String message)? network,
    TResult? Function(String message)? notFound,
    TResult? Function(String message)? invalidInput,
    TResult? Function(String message)? businessLogic,
    TResult? Function(String message)? unexpected,
    TResult? Function(String message)? sync,
    TResult? Function(String message)? permission,
    TResult? Function(String message)? validation,
  }) {
    return validation?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message)? database,
    TResult Function(String message)? network,
    TResult Function(String message)? notFound,
    TResult Function(String message)? invalidInput,
    TResult Function(String message)? businessLogic,
    TResult Function(String message)? unexpected,
    TResult Function(String message)? sync,
    TResult Function(String message)? permission,
    TResult Function(String message)? validation,
    required TResult orElse(),
  }) {
    if (validation != null) {
      return validation(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DatabaseFailure value) database,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(NotFoundFailure value) notFound,
    required TResult Function(InvalidInputFailure value) invalidInput,
    required TResult Function(BusinessLogicFailure value) businessLogic,
    required TResult Function(UnexpectedFailure value) unexpected,
    required TResult Function(SyncFailure value) sync,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(ValidationFailure value) validation,
  }) {
    return validation(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DatabaseFailure value)? database,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(NotFoundFailure value)? notFound,
    TResult? Function(InvalidInputFailure value)? invalidInput,
    TResult? Function(BusinessLogicFailure value)? businessLogic,
    TResult? Function(UnexpectedFailure value)? unexpected,
    TResult? Function(SyncFailure value)? sync,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(ValidationFailure value)? validation,
  }) {
    return validation?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DatabaseFailure value)? database,
    TResult Function(NetworkFailure value)? network,
    TResult Function(NotFoundFailure value)? notFound,
    TResult Function(InvalidInputFailure value)? invalidInput,
    TResult Function(BusinessLogicFailure value)? businessLogic,
    TResult Function(UnexpectedFailure value)? unexpected,
    TResult Function(SyncFailure value)? sync,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(ValidationFailure value)? validation,
    required TResult orElse(),
  }) {
    if (validation != null) {
      return validation(this);
    }
    return orElse();
  }
}

abstract class ValidationFailure implements Failure {
  const factory ValidationFailure(final String message) =
      _$ValidationFailureImpl;

  @override
  String get message;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ValidationFailureImplCopyWith<_$ValidationFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
