import 'package:freezed_annotation/freezed_annotation.dart';

part 'failures.freezed.dart';

/// Base class for all failures in the application
@freezed
class Failure with _$Failure {
  const factory Failure.database(String message) = DatabaseFailure;
  const factory Failure.network(String message) = NetworkFailure;
  const factory Failure.notFound(String message) = NotFoundFailure;
  const factory Failure.invalidInput(String message) = InvalidInputFailure;
  const factory Failure.businessLogic(String message) = BusinessLogicFailure;
  const factory Failure.unexpected(String message) = UnexpectedFailure;
  const factory Failure.sync(String message) = SyncFailure;
  const factory Failure.permission(String message) = PermissionFailure;
  const factory Failure.validation(String message) = ValidationFailure;
}
