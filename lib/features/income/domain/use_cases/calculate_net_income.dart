import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';

import '../entities/income.dart';

/// Use case for calculating net income and derived values
class CalculateNetIncome {
  /// Calculate all derived values for an income record
  Either<Failure, Income> call(Income income) {
    try {
      // Return the income with calculated values
      // Note: The calculated values are available through getters in the Income entity
      return Right(income);
    } catch (e) {
      return Left(Failure.businessLogic('Failed to calculate net income: $e'));
    }
  }

  /// Validate income data before calculation
  Either<Failure, bool> validateIncomeData(Income income) {
    try {
      // Check if initial mileage is provided
      if (income.initialMileage <= 0) {
        return const Left(
          Failure.validation('Initial mileage must be greater than 0'),
        );
      }

      // Check if final mileage is greater than initial mileage (when provided)
      if (income.finalMileage > 0 &&
          income.finalMileage < income.initialMileage) {
        return const Left(
          Failure.validation(
            'Final mileage cannot be less than initial mileage',
          ),
        );
      }

      // Check if at least one initial balance is provided
      if (!income.hasInitialBalance) {
        return const Left(
          Failure.validation('At least one initial balance must be provided'),
        );
      }

      return const Right(true);
    } catch (e) {
      return Left(Failure.validation('Validation failed: $e'));
    }
  }

  /// Calculate summary statistics for a list of income records
  Either<Failure, Map<String, double>> calculateSummaryStats(
    List<Income> incomes,
  ) {
    try {
      if (incomes.isEmpty) {
        return const Right({
          'totalNetIncome': 0.0,
          'averageNetIncome': 0.0,
          'highestIncome': 0.0,
          'lowestIncome': 0.0,
          'totalMileage': 0.0,
          'averageMileage': 0.0,
        });
      }

      final netIncomes = incomes
          .where((income) => income.netIncome != null)
          .map((income) => income.netIncome!)
          .toList();

      final mileages = incomes
          .where((income) => income.mileage != null)
          .map((income) => income.mileage!.toDouble())
          .toList();

      final totalNetIncome = netIncomes.fold(
        0.0,
        (sum, income) => sum + income,
      );
      final averageNetIncome = netIncomes.isNotEmpty
          ? totalNetIncome / netIncomes.length
          : 0.0;
      final highestIncome = netIncomes.isNotEmpty
          ? netIncomes.reduce((a, b) => a > b ? a : b)
          : 0.0;
      final lowestIncome = netIncomes.isNotEmpty
          ? netIncomes.reduce((a, b) => a < b ? a : b)
          : 0.0;

      final totalMileage = mileages.fold(0.0, (sum, mileage) => sum + mileage);
      final averageMileage = mileages.isNotEmpty
          ? totalMileage / mileages.length
          : 0.0;

      return Right({
        'totalNetIncome': totalNetIncome,
        'averageNetIncome': averageNetIncome,
        'highestIncome': highestIncome,
        'lowestIncome': lowestIncome,
        'totalMileage': totalMileage,
        'averageMileage': averageMileage,
      });
    } catch (e) {
      return Left(
        Failure.businessLogic('Failed to calculate summary statistics: $e'),
      );
    }
  }
}
