import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/repositories/repository_interfaces.dart';
import '../entities/income.dart';

/// Repository interface for Income operations
abstract class IIncomeRepository extends IRepository<Income, int>
    implements
        IDateRangeRepository<Income, int>,
        ISyncableRepository<Income, int>,
        ISoftDeleteRepository<Income, int> {
  /// Get paginated income records
  Future<Either<Failure, List<Income>>> getPaginated({
    int page = 1,
    int limit = 20,
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Get income records count
  Future<Either<Failure, int>> getCount({
    DateTime? startDate,
    DateTime? endDate,
    bool includeDeleted = false,
  });

  /// Get highest mileage from all records
  Future<Either<Failure, int>> getHighestMileage();

  /// Get income summary for date range
  Future<Either<Failure, IncomeSummary>> getSummary({
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Get income records for chart/trends
  Future<Either<Failure, List<Income>>> getForTrends({
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  });

  /// Search income records
  Future<Either<Failure, List<Income>>> search({
    String? query,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  });

  /// Get incomplete income records (missing final values)
  Future<Either<Failure, List<Income>>> getIncomplete();

  /// Get recent income records
  Future<Either<Failure, List<Income>>> getRecent({int limit = 10});
}

/// Income summary data class
class IncomeSummary {
  final double totalNetIncome;
  final int recordCount;
  final int totalRecordCount;
  final double highestIncome;
  final double lowestIncome;
  final double averageIncome;
  final int totalMileage;
  final double totalInitialCapital;
  final double totalFinalResult;

  // Additional computed properties
  final int count;
  final double averageNetIncome;
  final double averageMileage;

  const IncomeSummary({
    required this.totalNetIncome,
    required this.recordCount,
    required this.totalRecordCount,
    required this.highestIncome,
    required this.lowestIncome,
    required this.averageIncome,
    required this.totalMileage,
    required this.totalInitialCapital,
    required this.totalFinalResult,
    required this.count,
    required this.averageNetIncome,
    required this.averageMileage,
  });
}
