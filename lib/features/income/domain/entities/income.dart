import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../../core/utils/calculations.dart';

part 'income.freezed.dart';
part 'income.g.dart';

/// Income entity representing a daily income record
@freezed
class Income with _$Income {
  const factory Income({
    int? id,
    required DateTime date,
    required int initialMileage,
    required int finalMileage,
    required double initialGopay,
    required double initialBca,
    required double initialCash,
    required double initialOvo,
    required double initialBri,
    required double initialRekpon,
    required double finalGopay,
    required double finalBca,
    required double finalCash,
    required double finalOvo,
    required double finalBri,
    required double finalRekpon,
    String? uuid,
    @Default(false) bool isDeleted,
    @Default(false) bool isSynced,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
  }) = _Income;

  const Income._();

  /// Calculate initial capital from all initial balance values
  double? get initialCapital {
    return Calculations.calculateInitialCapital(
      initialGopay: initialGopay,
      initialBca: initialBca,
      initialCash: initialCash,
      initialOvo: initialOvo,
      initialBri: initialBri,
      initialRekpon: initialRekpon,
    );
  }

  /// Calculate final result from all final balance values
  double? get finalResult {
    return Calculations.calculateFinalResult(
      finalGopay: finalGopay,
      finalBca: finalBca,
      finalCash: finalCash,
      finalOvo: finalOvo,
      finalBri: finalBri,
      finalRekpon: finalRekpon,
    );
  }

  /// Calculate mileage difference
  int? get mileage {
    if (finalMileage == 0 || initialMileage == 0) return null;
    return Calculations.calculateMileage(
      finalMileage: finalMileage,
      initialMileage: initialMileage,
    );
  }

  /// Calculate net income
  double? get netIncome {
    final initial = initialCapital;
    final final_ = finalResult;
    if (initial == null || final_ == null) return null;
    return Calculations.calculateNetIncome(
      finalResult: final_,
      initialCapital: initial,
    );
  }

  /// Check if the income record is complete (has final values)
  bool get isComplete {
    return finalMileage > 0 &&
        (finalGopay != 0 ||
            finalBca != 0 ||
            finalCash != 0 ||
            finalOvo != 0 ||
            finalBri != 0 ||
            finalRekpon != 0);
  }

  /// Check if the income record has any initial balance
  bool get hasInitialBalance {
    return initialGopay != 0 ||
        initialBca != 0 ||
        initialCash != 0 ||
        initialOvo != 0 ||
        initialBri != 0 ||
        initialRekpon != 0;
  }

  /// Check if the income record has any final balance
  bool get hasFinalBalance {
    return finalGopay != 0 ||
        finalBca != 0 ||
        finalCash != 0 ||
        finalOvo != 0 ||
        finalBri != 0 ||
        finalRekpon != 0;
  }

  factory Income.fromJson(Map<String, dynamic> json) => _$IncomeFromJson(json);
}
