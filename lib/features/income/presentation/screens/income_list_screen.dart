import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/date_helper.dart';

import '../providers/income_providers.dart';
import '../widgets/income_card.dart';
import '../widgets/income_summary_card.dart';
import '../widgets/date_range_selector.dart';
import 'income_form_screen.dart';

/// Main screen displaying list of income records
class IncomeListScreen extends HookConsumerWidget {
  const IncomeListScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final scrollController = useScrollController();
    final startDate = useState<DateTime?>(null);
    final endDate = useState<DateTime?>(null);
    final currentOffset = useState(0);
    final allIncomes = useState<List<dynamic>>([]);
    final isLoadingMore = useState(false);

    // Watch providers
    final incomeListAsync = ref.watch(
      incomeListProvider({
        'limit': 20,
        'offset': currentOffset.value,
        'startDate': startDate.value,
        'endDate': endDate.value,
      }),
    );

    final summaryAsync = ref.watch(
      incomeSummaryProvider({
        'startDate': startDate.value,
        'endDate': endDate.value,
      }),
    );

    // Scroll listener for pagination
    useEffect(() {
      void onScroll() {
        if (scrollController.position.pixels >=
            scrollController.position.maxScrollExtent - 200) {
          if (!isLoadingMore.value) {
            isLoadingMore.value = true;
            currentOffset.value += 20;
          }
        }
      }

      scrollController.addListener(onScroll);
      return () => scrollController.removeListener(onScroll);
    }, [scrollController]);

    // Handle new data
    useEffect(() {
      incomeListAsync.whenData((newIncomes) {
        if (currentOffset.value == 0) {
          allIncomes.value = newIncomes;
        } else {
          allIncomes.value = [...allIncomes.value, ...newIncomes];
        }
        isLoadingMore.value = false;
      });
      return null;
    }, [incomeListAsync]);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Income Records'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showDateRangeDialog(context, startDate, endDate),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              currentOffset.value = 0;
              ref.invalidate(incomeListProvider);
              ref.invalidate(incomeSummaryProvider);
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          currentOffset.value = 0;
          ref.invalidate(incomeListProvider);
          ref.invalidate(incomeSummaryProvider);
        },
        child: CustomScrollView(
          controller: scrollController,
          slivers: [
            // Summary card
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: summaryAsync.when(
                  data: (summary) => IncomeSummaryCard(summary: summary),
                  loading: () => const Card(
                    child: Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Center(child: CircularProgressIndicator()),
                    ),
                  ),
                  error: (error, stack) => Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text('Error loading summary: $error'),
                    ),
                  ),
                ),
              ),
            ),

            // Date range filter
            if (startDate.value != null || endDate.value != null)
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Row(
                        children: [
                          const Icon(Icons.filter_list, size: 16),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Filtered: ${startDate.value != null ? DateHelper.formatForDisplay(startDate.value!) : 'All'} - ${endDate.value != null ? DateHelper.formatForDisplay(endDate.value!) : 'All'}',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                          ),
                          TextButton(
                            onPressed: () {
                              startDate.value = null;
                              endDate.value = null;
                              currentOffset.value = 0;
                            },
                            child: const Text('Clear'),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),

            // Income list
            incomeListAsync.when(
              data: (incomes) {
                if (allIncomes.value.isEmpty) {
                  return const SliverFillRemaining(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.receipt_long,
                            size: 64,
                            color: Colors.grey,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'No income records found',
                            style: TextStyle(fontSize: 18, color: Colors.grey),
                          ),
                          SizedBox(height: 8),
                          Text(
                            'Tap the + button to add your first record',
                            style: TextStyle(color: Colors.grey),
                          ),
                        ],
                      ),
                    ),
                  );
                }

                return SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      if (index < allIncomes.value.length) {
                        final income = allIncomes.value[index];
                        return Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16.0,
                            vertical: 4.0,
                          ),
                          child: IncomeCard(
                            income: income,
                            onTap: () => _navigateToForm(context, income),
                            onDelete: () => _deleteIncome(context, ref, income),
                          ),
                        );
                      } else if (isLoadingMore.value) {
                        return const Padding(
                          padding: EdgeInsets.all(16.0),
                          child: Center(child: CircularProgressIndicator()),
                        );
                      }
                      return null;
                    },
                    childCount:
                        allIncomes.value.length + (isLoadingMore.value ? 1 : 0),
                  ),
                );
              },
              loading: () => const SliverFillRemaining(
                child: Center(child: CircularProgressIndicator()),
              ),
              error: (error, stack) => SliverFillRemaining(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.red,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Error loading income records',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        error.toString(),
                        textAlign: TextAlign.center,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          currentOffset.value = 0;
                          ref.invalidate(incomeListProvider);
                        },
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _navigateToForm(context, null),
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  void _showDateRangeDialog(
    BuildContext context,
    ValueNotifier<DateTime?> startDate,
    ValueNotifier<DateTime?> endDate,
  ) {
    showDialog(
      context: context,
      builder: (context) => DateRangeSelector(
        initialStartDate: startDate.value,
        initialEndDate: endDate.value,
        onDateRangeSelected: (start, end) {
          startDate.value = start;
          endDate.value = end;
        },
      ),
    );
  }

  void _navigateToForm(BuildContext context, dynamic income) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => IncomeFormScreen(entity: income)),
    );
  }

  void _deleteIncome(BuildContext context, WidgetRef ref, dynamic income) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Income Record'),
        content: Text(
          'Are you sure you want to delete the income record for ${DateHelper.formatForDisplay(income.date)}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();

              final repository = ref.read(incomeRepositoryProvider);
              final result = await repository.delete(income.id);

              result.fold(
                (failure) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Error: ${failure.message}'),
                        backgroundColor: AppColors.error,
                      ),
                    );
                  }
                },
                (_) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Income record deleted'),
                        backgroundColor: AppColors.success,
                      ),
                    );
                  }
                  ref.invalidate(incomeListProvider);
                  ref.invalidate(incomeSummaryProvider);
                },
              );
            },
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
