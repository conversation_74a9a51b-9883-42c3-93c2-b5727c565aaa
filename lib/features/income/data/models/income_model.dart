import 'package:drift/drift.dart';
import '../../../../core/datasources/app_database.dart';
import '../../domain/entities/income.dart';

/// Data model for Income that wraps the domain entity
class IncomeModel {
  final Income _income;

  const IncomeModel._(this._income);

  /// Create IncomeModel with all required parameters
  factory IncomeModel({
    int? id,
    required DateTime date,
    required int initialMileage,
    required int finalMileage,
    required double initialGopay,
    required double initialBca,
    required double initialCash,
    required double initialOvo,
    required double initialBri,
    required double initialRekpon,
    required double finalGopay,
    required double finalBca,
    required double finalCash,
    required double finalOvo,
    required double finalBri,
    required double finalRekpon,
    String? uuid,
    bool isDeleted = false,
    bool isSynced = false,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
  }) {
    return IncomeModel._(
      Income(
        id: id,
        date: date,
        initialMileage: initialMileage,
        finalMileage: finalMileage,
        initialGopay: initialGopay,
        initialBca: initialBca,
        initialCash: initialCash,
        initialOvo: initialOvo,
        initialBri: initialBri,
        initialRekpon: initialRekpon,
        finalGopay: finalGopay,
        finalBca: finalBca,
        finalCash: finalCash,
        finalOvo: finalOvo,
        finalBri: finalBri,
        finalRekpon: finalRekpon,
        uuid: uuid,
        isDeleted: isDeleted,
        isSynced: isSynced,
        createdAt: createdAt,
        updatedAt: updatedAt,
        deletedAt: deletedAt,
      ),
    );
  }

  /// Create IncomeModel from domain entity
  factory IncomeModel.fromEntity(Income income) {
    return IncomeModel._(income);
  }

  /// Create IncomeModel from Drift database row
  factory IncomeModel.fromDrift(IncomeTableData data) {
    return IncomeModel(
      id: data.id,
      date: data.date,
      initialMileage: data.initialMileage,
      finalMileage: data.finalMileage,
      initialGopay: data.initialGopay,
      initialBca: data.initialBca,
      initialCash: data.initialCash,
      initialOvo: data.initialOvo,
      initialBri: data.initialBri,
      initialRekpon: data.initialRekpon,
      finalGopay: data.finalGopay,
      finalBca: data.finalBca,
      finalCash: data.finalCash,
      finalOvo: data.finalOvo,
      finalBri: data.finalBri,
      finalRekpon: data.finalRekpon,
      uuid: data.uuid,
      isDeleted: data.isDeleted,
      isSynced: data.isSynced,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,
      deletedAt: data.deletedAt,
    );
  }

  /// Convert to Drift insertable companion
  IncomeTableCompanion toDrift() {
    return IncomeTableCompanion(
      id: _income.id != null ? Value(_income.id!) : const Value.absent(),
      date: Value(_income.date),
      initialMileage: Value(_income.initialMileage),
      finalMileage: Value(_income.finalMileage),
      initialGopay: Value(_income.initialGopay),
      initialBca: Value(_income.initialBca),
      initialCash: Value(_income.initialCash),
      initialOvo: Value(_income.initialOvo),
      initialBri: Value(_income.initialBri),
      initialRekpon: Value(_income.initialRekpon),
      finalGopay: Value(_income.finalGopay),
      finalBca: Value(_income.finalBca),
      finalCash: Value(_income.finalCash),
      finalOvo: Value(_income.finalOvo),
      finalBri: Value(_income.finalBri),
      finalRekpon: Value(_income.finalRekpon),
      uuid: Value(_income.uuid),
      isDeleted: Value(_income.isDeleted),
      isSynced: Value(_income.isSynced),
      createdAt: Value(_income.createdAt),
      updatedAt: Value(_income.updatedAt),
      deletedAt: Value(_income.deletedAt),
    );
  }

  /// Convert to domain entity
  Income toEntity() {
    return _income;
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return _income.toJson();
  }

  // Delegate getters to the wrapped Income entity
  int? get id => _income.id;
  DateTime get date => _income.date;
  int get initialMileage => _income.initialMileage;
  int get finalMileage => _income.finalMileage;
  double get initialGopay => _income.initialGopay;
  double get initialBca => _income.initialBca;
  double get initialCash => _income.initialCash;
  double get initialOvo => _income.initialOvo;
  double get initialBri => _income.initialBri;
  double get initialRekpon => _income.initialRekpon;
  double get finalGopay => _income.finalGopay;
  double get finalBca => _income.finalBca;
  double get finalCash => _income.finalCash;
  double get finalOvo => _income.finalOvo;
  double get finalBri => _income.finalBri;
  double get finalRekpon => _income.finalRekpon;
  String? get uuid => _income.uuid;
  bool get isDeleted => _income.isDeleted;
  bool get isSynced => _income.isSynced;
  DateTime? get createdAt => _income.createdAt;
  DateTime? get updatedAt => _income.updatedAt;
  DateTime? get deletedAt => _income.deletedAt;

  // Computed properties
  double get totalInitialCapital =>
      initialGopay +
      initialBca +
      initialCash +
      initialOvo +
      initialBri +
      initialRekpon;

  double get totalFinalResult =>
      finalGopay + finalBca + finalCash + finalOvo + finalBri + finalRekpon;

  double get netIncome => totalFinalResult - totalInitialCapital;

  int get mileage => finalMileage - initialMileage;

  // Aliases for compatibility with existing code
  double get initialCapital => totalInitialCapital;
  double get finalResult => totalFinalResult;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is IncomeModel &&
          runtimeType == other.runtimeType &&
          _income == other._income;

  @override
  int get hashCode => _income.hashCode;

  @override
  String toString() => 'IncomeModel($_income)';
}
