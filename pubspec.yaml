name: drivly
description: "Drivers Performance, Income Tracking and Spare Parts Monitoring App"
publish_to: 'none'
version: 0.1.0

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.2

  # State management
  flutter_hooks: ^0.20.0
  flutter_riverpod: ^2.3.6
  hooks_riverpod: ^2.3.6
  riverpod_annotation: ^2.1.1
  riverpod: ^2.0.0

  # Database
  drift: ^2.10.0
  sqlite3_flutter_libs: ^0.5.15
  path: ^1.8.3
  path_provider: ^2.0.15

  # Code generation
  freezed_annotation: ^2.2.0
  json_annotation: ^4.9.0

  # UI
  google_fonts: ^4.0.4
  flutter_svg: ^2.0.7
  intl: ^0.18.1
  dartz: ^0.10.1
  fl_chart: ^0.65.0

  # File operations
  sqflite: ^2.2.8+4
  shared_preferences: ^2.2.2
  device_info_plus: ^9.1.2
  file_picker: ^9.1.0
  permission_handler: ^11.0.1
  shimmer: ^3.0.0

  # Sync and Cloud
  supabase_flutter: ^1.10.25
  uuid: ^4.2.2
  connectivity_plus: ^5.0.2
  flutter_dotenv: ^5.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.4

  # Code generation
  build_runner: ^2.4.6
  drift_dev: ^2.10.0
  freezed: ^2.3.5
  json_serializable: ^6.7.0
  riverpod_generator: ^2.2.3

flutter:
  uses-material-design: true
  assets:
    - assets/icon/
    - assets/images/
    - .env

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icon/drivly_icon.png"
  min_sdk_android: 21
  web:
    generate: true
    image_path: "assets/icon/drivly_icon.png"
    background_color: "#hexcode"
    theme_color: "#hexcode"
  windows:
    generate: true
    image_path: "assets/icon/drivly_icon.png"
    icon_size: 48
  macos:
    generate: true
    image_path: "assets/icon/drivly_icon.png"
